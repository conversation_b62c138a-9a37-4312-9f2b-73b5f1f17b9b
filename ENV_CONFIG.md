# Environment Configuration Guide

## Overview

Alfred uses a two-tier environment configuration system to avoid repetition and make configuration management easier:

1. **Base Configuration** (`.env.base`) - Common settings across all environments
2. **Environment-Specific Configuration** (`.env.{ENV}`) - Environment-specific overrides

## Configuration Files

### .env.base
Contains common configuration shared across all environments:
- GCP project ID and location
- AI model selections
- Common MongoDB collections
- Standard feature flags

### .env.local
Local development overrides:
- UAT MongoDB connection
- UAT S3 bucket
- Development-specific settings

### .env.uat
UAT environment overrides:
- UAT MongoDB cluster connection
- UAT-specific bucket

### .env.prod
Production environment overrides:
- Production MongoDB cluster
- Production S3 bucket
- Production log level (WARNING instead of INFO)

### .env.otel
OpenTelemetry testing environment overrides:
- OTEL MongoDB database
- OTEL S3 bucket
- Same cluster as UAT/Production

## How It Works

1. The system first loads `.env.base`
2. Then loads `.env.{ENV}` which overrides any values from base
3. The `ENV` variable defaults to `local` if not set

## Usage

```bash
# Local development (default)
python run.py

# UAT environment
ENV=uat python run.py

# Production environment
ENV=prod python run.py

# OTEL testing environment
ENV=otel python run.py
```

## Benefits

- **DRY Principle**: No need to repeat common settings
- **Clear Separation**: Easy to see what's different between environments
- **Easy Maintenance**: Update common settings in one place
- **Reduced Errors**: Less chance of forgetting to update a setting across environments

## Adding New Variables

1. If the variable is the same across all environments, add it to `.env.base`
2. If it's environment-specific, add it to the respective `.env.{ENV}` file
3. Update `EnvConfig` class in `src/env_config.py` if it's a new required variable

## Example

If you need to add a new AI model configuration:

1. Add to `.env.base`:
   ```
   NEW_MODEL=gemini-2.0-ultra
   ```

2. If production needs a different model, override in `.env.prod`:
   ```
   NEW_MODEL=gemini-1.5-pro
   ```

The system will automatically use the correct value based on the environment.
